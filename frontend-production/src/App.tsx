import React, { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { NotificationContainer } from '@/components/ui/NotificationContainer';
import { ModalProvider } from '@/components/ui/Modal';
import { useAuthStore } from '@/stores/authStore';
import { initializeStores } from '@/stores';
import { initializeServices } from '@/services';
import { initializeDebugMode, isDebugMode, shouldSkipAuth, debugLog, isIllegalDebugUser, forceRemoveIllegalDebugUser } from '@/utils/debug';
import { detectAndFixUserStateInconsistency, validateUserAuthState } from '@/utils/userStateReset';
import router from '@/router';

const App: React.FC = () => {
  const { initializeAuth, setDebugUser, checkTokenExpiry } = useAuthStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize debug mode first
        if (isDebugMode()) {
          initializeDebugMode();
          debugLog.info('Initializing app in debug mode');
        }

        // Initialize stores
        const storeCleanup = await initializeStores();

        // Initialize external services
        await initializeServices();

        // 🔒 CRITICAL SECURITY: 检测并强制清除非法调试用户
        const performIllegalDebugUserCheck = () => {
          const { user } = useAuthStore.getState();

          // 检测非法调试用户
          if (user && isIllegalDebugUser(user)) {
            console.error('ILLEGAL DEBUG USER DETECTED IN APP INITIALIZATION!');
            console.warn('SECURITY: Force removing illegal debug user');

            // 强制清除非法调试用户
            forceRemoveIllegalDebugUser();
            return true; // 表示清除了非法调试用户
          }

          return false;
        };

        // 执行非法调试用户检查
        const illegalUserCleared = performIllegalDebugUserCheck();

        // Initialize authentication
        await initializeAuth();

        // 等待状态同步完成
        await new Promise(resolve => setTimeout(resolve, 200));

        // 🔒 SECURITY: 延迟检查数据一致性，避免误判临时状态
        console.log('🔍 检查用户状态一致性（认证初始化后）...');
        const hasInconsistency = detectAndFixUserStateInconsistency();

        if (hasInconsistency) {
          console.log('⚠️ 检测到数据不一致，已触发重置流程');
          return; // 重置流程会重新加载页面
        }

        // 🔍 验证认证状态的有效性（添加重试机制）
        let authValidationRetries = 0;
        const maxAuthRetries = 2;
        let isValidAuth = false;

        while (authValidationRetries <= maxAuthRetries && !isValidAuth) {
          try {
            isValidAuth = await validateUserAuthState();
            if (!isValidAuth && authValidationRetries < maxAuthRetries) {
              console.log(`⚠️ 认证状态验证失败，重试 ${authValidationRetries + 1}/${maxAuthRetries}`);
              await new Promise(resolve => setTimeout(resolve, 1000));
              authValidationRetries++;
            }
          } catch (error) {
            console.error('认证状态验证出错:', error);
            authValidationRetries++;
            if (authValidationRetries <= maxAuthRetries) {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        }

        if (!isValidAuth) {
          console.log('⚠️ 认证状态验证最终失败，已触发重置流程');
          return; // 验证失败会触发重置
        }

        // 再次执行非法调试用户检查（认证后）
        performIllegalDebugUserCheck();

        // 🔒 CRITICAL: 只有在开发环境且明确启用调试时才设置调试用户
        const { isAuthenticated, user } = useAuthStore.getState();
        
        // 详细日志记录调试状态（仅在开发环境）
        if (isDebugMode()) {
          debugLog.info('Debug status check:', {
            isDebugMode: isDebugMode(),
            shouldSkipAuth: shouldSkipAuth(),
            isAuthenticated,
            hasUser: !!user,
            userEmail: user?.email,
            debugSkipAuthEnv: import.meta.env.VITE_DEBUG_SKIP_AUTH,
            environment: import.meta.env.VITE_ENVIRONMENT,
            hostname: window.location.hostname
          });
        }
        
        // 🔒 ABSOLUTE PROTECTION: 生产环境绝对不设置调试用户
        if (import.meta.env.VITE_ENVIRONMENT === 'production') {
          console.log('🔒 Production mode: Debug user creation blocked');
          return; // 直接返回，不执行任何调试用户逻辑
        }
        
        // 🔒 DEVELOPMENT ONLY: 调试用户逻辑
        if (shouldSkipAuth() && !isAuthenticated && !user && !illegalUserCleared) {
          debugLog.info('Development: Setting debug user (no real user found)');
          setDebugUser('PREMIUM');
        } else if (isAuthenticated && user) {
          debugLog.info('User authenticated, keeping real user:', user.email);
          
          // 最后一道防线：检测到非法调试用户时强制清除
          if (isIllegalDebugUser(user)) {
            console.error('CRITICAL: Illegal debug user detected! Force removing.');
            forceRemoveIllegalDebugUser();
          }
        }

        console.log('✅ StoryWeaver app initialized successfully');

        // Set up periodic token check (every 5 minutes)
        const tokenCheckInterval = setInterval(() => {
          checkTokenExpiry();
        }, 5 * 60 * 1000);

        // Cleanup function
        return () => {
          if (storeCleanup) {
            storeCleanup();
          }
          clearInterval(tokenCheckInterval);
        };
      } catch (error) {
        console.error('❌ Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [initializeAuth]);

  return (
    <ErrorBoundary>
      <ModalProvider>
        <div className="App">
          <RouterProvider router={router} />
          <NotificationContainer />
        </div>
      </ModalProvider>
    </ErrorBoundary>
  );
};

export default App;

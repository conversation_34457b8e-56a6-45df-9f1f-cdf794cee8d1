/**
 * Story Generation WebSocket Client
 * 与Durable Objects进行实时通信的客户端
 */

export interface Task {
  id: string;
  type: 'text' | 'image' | 'audio';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  error?: string;
}

export interface StoryGenerationParams {
  characterName: string;
  age?: number;
  traits?: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
}

export interface WebSocketMessage {
  type: 'storyStarted' | 'taskUpdate' | 'taskProgress' | 'storyCompleted' | 'storyFailed' | 'storyCancelled' |
        'pageContentReady' | 'generationProgress' | 'storyStructure' | 'connected' | 'disconnected' | 'error' | 'connectionFailed';
  storyId: string;
  timestamp: number;
  [key: string]: any;
}

// 扩展的WebSocket消息类型
export interface PageContentMessage extends WebSocketMessage {
  type: 'pageContentReady';
  storyId: string;
  pageIndex: number;
  content: {
    text?: string;
    imageUrl?: string;
    audioUrl?: string;
    imagePrompt?: string;
  };
  timestamp: number;
}

export interface GenerationProgressMessage extends WebSocketMessage {
  type: 'generationProgress';
  storyId: string;
  stage: 'text' | 'image' | 'audio';
  progress: {
    completed: number;
    total: number;
    currentItem?: string;
    percentage?: number;
  };
  timestamp: number;
}

export interface StoryStructureMessage extends WebSocketMessage {
  type: 'storyStructure';
  storyId: string;
  totalPages: number;
  title: string;
  timestamp: number;
}

export class StoryGenerationClient {
  private ws: WebSocket | null = null;
  private listeners: Map<string, Function[]> = new Map();
  private storyId: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  constructor(storyId: string) {
    this.storyId = storyId;
    console.log('🏗️ StoryGenerationClient created for story:', storyId);
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    console.log('🔌 StoryGenerationClient.connect() called for story:', this.storyId);
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
        console.log('⚠️ Already connecting or connected, skipping...');
        resolve();
        return;
      }

      this.isConnecting = true;
      console.log('🔧 Building WebSocket URL...');

      // 构建WebSocket URL - 开发环境和生产环境适配
      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8787/api';
      console.log('📍 Base URL from env:', baseUrl);
      const isLocal = baseUrl.includes('localhost');
      const protocol = isLocal ? 'ws://' : 'wss://';
      console.log('🌐 Protocol:', protocol, '(isLocal:', isLocal, ')');

      // 从API URL中提取主机名，移除/api后缀
      let host: string;
      if (isLocal) {
        host = 'localhost:8787';
      } else {
        // 从 https://storyweaver-api.stawky.workers.dev/api 提取主机名
        const url = new URL(baseUrl);
        host = url.host;
      }
      console.log('🏠 Host:', host);

      const wsUrl = `${protocol}${host}/ai-queue/${this.storyId}/websocket`;

      console.log('🔗 Final WebSocket URL:', wsUrl);

      try {
        console.log('🚀 Creating WebSocket connection...');
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('✅ WebSocket connected successfully for story:', this.storyId);
          console.log('WebSocket URL:', wsUrl);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.notifyListeners('connected', { storyId: this.storyId });
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            console.log('WebSocket message received:', message);
            this.notifyListeners(message.type, message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;
          
          this.notifyListeners('disconnected', { 
            storyId: this.storyId, 
            code: event.code, 
            reason: event.reason 
          });

          // 自动重连
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else {
            console.error('Max reconnection attempts reached');
            this.notifyListeners('connectionFailed', { storyId: this.storyId });
          }
        };

        this.ws.onerror = (error) => {
          console.error('❌ WebSocket error for story:', this.storyId);
          console.error('WebSocket URL:', wsUrl);
          console.error('Error details:', error);
          this.isConnecting = false;
          this.notifyListeners('error', { storyId: this.storyId, error });
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        console.error('Failed to create WebSocket:', error);
        reject(error);
      }
    });
  }

  /**
   * 安排重连
   */
  private scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.ws || this.ws.readyState === WebSocket.CLOSED) {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * 开始故事生成
   */
  async startGeneration(params: StoryGenerationParams): Promise<any> {
    console.log('🚀 startGeneration called for story:', this.storyId);
    console.log('📋 Generation params:', params);

    // 使用统一的API URL配置，移除/api后缀
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://storyweaver-api.stawky.workers.dev/api';
    console.log('📍 API base URL from env:', apiBaseUrl);
    const baseUrl = apiBaseUrl.replace('/api', '');
    console.log('🔧 Base URL after removing /api:', baseUrl);
    const url = `${baseUrl}/ai-queue/${this.storyId}/generate`;
    console.log('🔗 Final generation URL:', url);

    console.log('🎯 Starting story generation:', { storyId: this.storyId, params });
    
    try {
      console.log('📤 Sending POST request to start generation...');
      const requestBody = {
        storyId: this.storyId,
        ...params
      };
      console.log('📦 Request body:', requestBody);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 Response status:', response.status, response.statusText);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response error:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Story generation started successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Failed to start story generation:', error);
      throw error;
    }
  }

  /**
   * 获取任务状态
   */
  async getStatus(): Promise<any> {
    const baseUrl = import.meta.env.VITE_API_URL || 'https://storyweaver-api.stawky.workers.dev';
    const url = `${baseUrl}/ai-queue/${this.storyId}/status?storyId=${this.storyId}`;
    
    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get story status:', error);
      throw error;
    }
  }

  /**
   * 取消故事生成
   */
  async cancelGeneration(): Promise<any> {
    // 使用统一的API URL配置，移除/api后缀
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://storyweaver-api.stawky.workers.dev/api';
    const baseUrl = apiBaseUrl.replace('/api', '');
    const url = `${baseUrl}/ai-queue/${this.storyId}/cancel`;
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ storyId: this.storyId })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to cancel story generation:', error);
      throw error;
    }
  }

  /**
   * 监听连接事件
   */
  onConnected(callback: (data: any) => void) {
    this.addListener('connected', callback);
  }

  /**
   * 监听断开连接事件
   */
  onDisconnected(callback: (data: any) => void) {
    this.addListener('disconnected', callback);
  }

  /**
   * 监听连接失败事件
   */
  onConnectionFailed(callback: (data: any) => void) {
    this.addListener('connectionFailed', callback);
  }

  /**
   * 监听故事开始事件
   */
  onStoryStarted(callback: (data: { storyId: string; tasks: Task[] }) => void) {
    this.addListener('storyStarted', callback);
  }

  /**
   * 监听任务更新事件
   */
  onTaskUpdate(callback: (data: { storyId: string; task: Task }) => void) {
    this.addListener('taskUpdate', callback);
  }

  /**
   * 监听任务进度事件
   */
  onTaskProgress(callback: (data: { taskId: string; storyId: string; progress: number; status: string }) => void) {
    this.addListener('taskProgress', callback);
  }

  /**
   * 监听故事完成事件
   */
  onStoryCompleted(callback: (data: { storyId: string; story: any }) => void) {
    this.addListener('storyCompleted', callback);
  }

  /**
   * 监听故事失败事件
   */
  onStoryFailed(callback: (data: { storyId: string; error?: string; failedTasks?: any[] }) => void) {
    this.addListener('storyFailed', callback);
  }

  /**
   * 监听故事取消事件
   */
  onStoryCancelled(callback: (data: { storyId: string }) => void) {
    this.addListener('storyCancelled', callback);
  }

  /**
   * 监听故事结构事件
   */
  onStoryStructure(callback: (data: StoryStructureMessage) => void) {
    this.addListener('storyStructure', callback);
  }

  /**
   * 监听页面内容就绪事件
   */
  onPageContentReady(callback: (data: PageContentMessage) => void) {
    this.addListener('pageContentReady', callback);
  }

  /**
   * 监听生成进度事件
   */
  onGenerationProgress(callback: (data: GenerationProgressMessage) => void) {
    this.addListener('generationProgress', callback);
  }

  /**
   * 监听WebSocket错误事件
   */
  onError(callback: (data: any) => void) {
    this.addListener('error', callback);
  }

  /**
   * 添加事件监听器
   */
  private addListener(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听器
   */
  removeListener(event: string, callback: Function) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(event: string, data: any) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} listener:`, error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
    this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自动重连
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * 获取连接状态字符串
   */
  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'unknown';
    }
  }
}

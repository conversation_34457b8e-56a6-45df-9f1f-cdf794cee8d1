export interface Story {
  id: string;
  title: string;
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  pages: StoryPage[];
  coverImageUrl?: string;
  audioUrl?: string;
  status: 'draft' | 'generating' | 'completed' | 'failed';
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface StoryPage {
  id: string;
  pageNumber: number;
  text: string;
  imageUrl?: string;
  audioUrl?: string;
  imagePrompt?: string;
  generatedAt?: string;
}

export interface CreateStoryRequest {
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  customPrompt?: string;
}

// 渐进式内容展示相关类型
export interface ProgressivePageState {
  pageIndex: number;
  text: {
    content?: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    generatedAt?: string;
  };
  image: {
    url?: string;
    prompt?: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    generatedAt?: string;
  };
  audio: {
    url?: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    generatedAt?: string;
  };
}

export interface ProgressiveStoryState {
  storyId: string;
  title?: string;
  totalPages?: number;
  pages: ProgressivePageState[];
  overallStatus: 'initializing' | 'generating' | 'completed' | 'error';
  currentStage: 'text' | 'image' | 'audio' | 'completed';
  progress: {
    text: { completed: number; total: number };
    image: { completed: number; total: number };
    audio: { completed: number; total: number };
  };
}

export interface UpdateStoryRequest {
  title?: string;
  status?: Story['status'];
}

export interface StoryGenerationProgress {
  storyId: string;
  stage: 'text' | 'images' | 'audio' | 'completed';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  error?: string;
}

export interface StoryFilters {
  status?: Story['status'];
  theme?: string;
  characterAge?: number;
  query?: string;
  sortBy?: 'newest' | 'oldest' | 'title' | 'character';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

export interface StoryStats {
  total: number;
  completed: number;
  generating: number;
  failed: number;
  byTheme: Record<string, number>;
  byAge: Record<string, number>;
}

// Story themes and settings
export const STORY_THEMES = [
  'adventure',
  'friendship',
  'learning',
  'family',
  'animals',
  'fantasy',
  'science',
  'mystery'
] as const;

export const STORY_SETTINGS = [
  'forest',
  'ocean',
  'city',
  'space',
  'home',
  'school',
  'playground',
  'magical_land'
] as const;

export const STORY_STYLES = [
  'simple',
  'detailed',
  'poetic',
  'humorous',
  'educational',
  'adventurous'
] as const;

export const VOICE_OPTIONS = [
  'gentle_female',
  'warm_male',
  'child_friendly',
  'storyteller',
  'narrator'
] as const;

export type StoryTheme = typeof STORY_THEMES[number];
export type StorySetting = typeof STORY_SETTINGS[number];
export type StoryStyle = typeof STORY_STYLES[number];
export type VoiceOption = typeof VOICE_OPTIONS[number];
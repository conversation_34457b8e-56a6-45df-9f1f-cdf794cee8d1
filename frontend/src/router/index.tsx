import React, { Suspense } from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ProtectedRoute } from './ProtectedRoute';

// Lazy load pages for better performance
const HomePage = React.lazy(() => import('@/pages/HomePage'));
const AuthPage = React.lazy(() => import('@/pages/AuthPage'));
const CreateStoryPage = React.lazy(() => import('@/pages/CreateStoryPage'));
const MyStoriesPage = React.lazy(() => import('@/pages/MyStoriesPage'));
const PricingPage = React.lazy(() => import('@/pages/PricingPage'));
const NotFoundPage = React.lazy(() => import('@/pages/NotFoundPage'));

// Additional pages (to be created)
const StoryDetailPage = React.lazy(() => import('@/pages/StoryDetailPage'));
const ProgressiveStoryPage = React.lazy(() => import('@/pages/ProgressiveStoryPage'));
const ProfilePage = React.lazy(() => import('@/pages/ProfilePage'));
const SettingsPage = React.lazy(() => import('@/pages/SettingsPage'));
const HelpPage = React.lazy(() => import('@/pages/HelpPage'));
const TermsPage = React.lazy(() => import('@/pages/TermsPage'));
const PrivacyPage = React.lazy(() => import('@/pages/PrivacyPage'));

// New pages
const ContactPage = React.lazy(() => import('@/pages/ContactPage'));
const BookCustomizationPage = React.lazy(() => import('@/pages/BookCustomizationPage'));
const FAQPage = React.lazy(() => import('@/pages/FAQPage'));
const TutorialPage = React.lazy(() => import('@/pages/TutorialPage'));

// Debug/Test pages
const StripeTestPage = React.lazy(() => import('@/pages/StripeTestPage'));

// Payment pages
const PaymentSuccessPage = React.lazy(() => import('@/pages/PaymentSuccessPage'));
const PaymentFailedPage = React.lazy(() => import('@/pages/PaymentFailedPage'));

// Loading component for suspense
const PageLoader: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingSpinner size="lg" label="加载页面中..." />
  </div>
);

// Wrapper component with error boundary and suspense
const PageWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ErrorBoundary>
    <Suspense fallback={<PageLoader />}>
      {children}
    </Suspense>
  </ErrorBoundary>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <PageWrapper>
        <HomePage />
      </PageWrapper>
    ),
  },
  {
    path: '/auth',
    element: (
      <PageWrapper>
        <AuthPage />
      </PageWrapper>
    ),
  },
  {
    path: '/pricing',
    element: (
      <PageWrapper>
        <PricingPage />
      </PageWrapper>
    ),
  },
  {
    path: '/help',
    element: (
      <PageWrapper>
        <HelpPage />
      </PageWrapper>
    ),
  },
  {
    path: '/terms',
    element: (
      <PageWrapper>
        <TermsPage />
      </PageWrapper>
    ),
  },
  {
    path: '/privacy',
    element: (
      <PageWrapper>
        <PrivacyPage />
      </PageWrapper>
    ),
  },
  {
    path: '/contact',
    element: (
      <PageWrapper>
        <ContactPage />
      </PageWrapper>
    ),
  },
  {
    path: '/book-customization',
    element: (
      <PageWrapper>
        <BookCustomizationPage />
      </PageWrapper>
    ),
  },
  {
    path: '/faq',
    element: (
      <PageWrapper>
        <FAQPage />
      </PageWrapper>
    ),
  },
  {
    path: '/tutorial',
    element: (
      <PageWrapper>
        <TutorialPage />
      </PageWrapper>
    ),
  },
  // Protected routes (require authentication)
  {
    path: '/create',
    element: (
      <ProtectedRoute>
        <PageWrapper>
          <CreateStoryPage />
        </PageWrapper>
      </ProtectedRoute>
    ),
  },
  {
    path: '/my-stories',
    element: (
      <ProtectedRoute>
        <PageWrapper>
          <MyStoriesPage />
        </PageWrapper>
      </ProtectedRoute>
    ),
  },
  {
    path: '/stories/:id',
    element: (
      <ProtectedRoute>
        <PageWrapper>
          <StoryDetailPage />
        </PageWrapper>
      </ProtectedRoute>
    ),
  },
  {
    path: '/stories/:id/live',
    element: (
      <ProtectedRoute>
        <PageWrapper>
          <ProgressiveStoryPage />
        </PageWrapper>
      </ProtectedRoute>
    ),
  },
  {
    path: '/profile',
    element: (
      <ProtectedRoute>
        <PageWrapper>
          <ProfilePage />
        </PageWrapper>
      </ProtectedRoute>
    ),
  },
  {
    path: '/settings',
    element: (
      <ProtectedRoute>
        <PageWrapper>
          <SettingsPage />
        </PageWrapper>
      </ProtectedRoute>
    ),
  },
  // Payment routes
  {
    path: '/payment/success',
    element: (
      <PageWrapper>
        <PaymentSuccessPage />
      </PageWrapper>
    ),
  },
  {
    path: '/payment/failed',
    element: (
      <PageWrapper>
        <PaymentFailedPage />
      </PageWrapper>
    ),
  },
  // Debug/Test routes
  {
    path: '/test/stripe',
    element: (
      <PageWrapper>
        <StripeTestPage />
      </PageWrapper>
    ),
  },
  // Redirects
  {
    path: '/dashboard',
    element: <Navigate to="/my-stories" replace />,
  },
  {
    path: '/login',
    element: <Navigate to="/auth" replace />,
  },
  {
    path: '/signup',
    element: <Navigate to="/auth" replace />,
  },
  // 404 page
  {
    path: '*',
    element: (
      <PageWrapper>
        <NotFoundPage />
      </PageWrapper>
    ),
  },
]);

export default router;

{"version": 3, "file": "gatherLogicalOperands.d.ts", "sourceRoot": "", "sources": ["../../../src/rules/prefer-optional-chain-utils/gatherLogicalOperands.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,iCAAiC,EACjC,QAAQ,EACT,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,oCAAoC,CAAC;AAYrE,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAS/E,0BAAkB,eAAe;IAC/B,KAAK,UAAU;IACf,OAAO,YAAY;CACpB;AACD,0BAAkB,qBAAqB;IACrC,oCAAoC;IACpC,uBAAuB,4BAA4B;IACnD,oCAAoC;IACpC,oBAAoB,yBAAyB;IAE7C,mBAAmB;IACnB,kBAAkB,uBAAuB;IACzC,mBAAmB;IACnB,eAAe,oBAAoB;IAEnC,oDAAoD;IACpD,uBAAuB,4BAA4B;IACnD,oDAAoD;IACpD,oBAAoB,yBAAyB;IAE7C,WAAW;IACX,UAAU,eAAe;IACzB,UAAU;IACV,OAAO,YAAY;CACpB;AACD,MAAM,WAAW,YAAY;IAC3B,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC;IAC5B,cAAc,EAAE,qBAAqB,CAAC;IACtC,MAAM,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC;IAC1B,IAAI,EAAE,eAAe,CAAC,KAAK,CAAC;CAC7B;AACD,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC;CAC/B;AACD,KAAK,OAAO,GAAG,cAAc,GAAG,YAAY,CAAC;AAwD7C,wBAAgB,qBAAqB,CACnC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,EAChC,cAAc,EAAE,iCAAiC,EACjD,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,EAChC,OAAO,EAAE,0BAA0B,GAClC;IACD,iBAAiB,EAAE,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IACnD,QAAQ,EAAE,OAAO,EAAE,CAAC;CACrB,CA0PA"}
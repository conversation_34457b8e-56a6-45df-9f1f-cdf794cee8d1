#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/workerd@1.20250408.0/node_modules/workerd/bin/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/workerd@1.20250408.0/node_modules/workerd/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/workerd@1.20250408.0/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/workerd@1.20250408.0/node_modules/workerd/bin/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/workerd@1.20250408.0/node_modules/workerd/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/workerd@1.20250408.0/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../workerd/bin/workerd" "$@"
else
  exec node  "$basedir/../../../workerd/bin/workerd" "$@"
fi

#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/miniflare@3.20250408.2/node_modules/miniflare/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/miniflare@3.20250408.2/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/miniflare@3.20250408.2/node_modules/miniflare/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/miniflare@3.20250408.2/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../miniflare/bootstrap.js" "$@"
else
  exec node  "$basedir/../../../miniflare/bootstrap.js" "$@"
fi

import type { TSESTree } from '@typescript-eslint/types';
import type { ScopeManager } from '../ScopeManager';
import type { Scope } from './Scope';
import { ScopeBase } from './ScopeBase';
import { ScopeType } from './ScopeType';
declare class ConditionalTypeScope extends ScopeBase<ScopeType.conditionalType, TSESTree.TSConditionalType, Scope> {
    constructor(scopeManager: ScopeManager, upperScope: ConditionalTypeScope['upper'], block: ConditionalTypeScope['block']);
}
export { ConditionalTypeScope };
//# sourceMappingURL=ConditionalTypeScope.d.ts.map
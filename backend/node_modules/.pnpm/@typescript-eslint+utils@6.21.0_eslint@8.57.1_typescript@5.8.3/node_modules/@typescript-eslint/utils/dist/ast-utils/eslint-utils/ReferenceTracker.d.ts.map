{"version": 3, "file": "ReferenceTracker.d.ts", "sourceRoot": "", "sources": ["../../../src/ast-utils/eslint-utils/ReferenceTracker.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,KAAK,QAAQ,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEhD,QAAA,MAAM,oBAAoB,EAAE,OAAO,MAA0C,CAAC;AAC9E,QAAA,MAAM,oBAAoB,EAAE,OAAO,MAA0C,CAAC;AAC9E,QAAA,MAAM,yBAAyB,EAAE,OAAO,MACA,CAAC;AACzC,QAAA,MAAM,mBAAmB,EAAE,OAAO,MAAyC,CAAC;AAE5E,UAAU,gBAAgB;IACxB;;;;;OAKG;IACH,uBAAuB,CAAC,CAAC,EACvB,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,GACrC,gBAAgB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAExD;;;;;OAKG;IACH,oBAAoB,CAAC,CAAC,EACpB,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,GACrC,gBAAgB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAExD;;;;;OAKG;IACH,oBAAoB,CAAC,CAAC,EACpB,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,GACrC,gBAAgB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;CACzD;AACD,UAAU,sBAAsB;IAC9B,KACE,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,EACjC,OAAO,CAAC,EAAE;QACR;;;;WAIG;QACH,IAAI,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC;QAC3B;;WAEG;QACH,iBAAiB,CAAC,EAAE,SAAS,MAAM,EAAE,CAAC;KACvC,GACA,gBAAgB,CAAC;IAEpB,QAAQ,CAAC,IAAI,EAAE,OAAO,oBAAoB,CAAC;IAC3C,QAAQ,CAAC,IAAI,EAAE,OAAO,oBAAoB,CAAC;IAC3C,QAAQ,CAAC,SAAS,EAAE,OAAO,yBAAyB,CAAC;IACrD,QAAQ,CAAC,GAAG,EAAE,OAAO,mBAAmB,CAAC;CAC1C;AAED,kBAAU,gBAAgB,CAAC;IACzB,KAAY,IAAI,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAClD,KAAY,IAAI,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAClD,KAAY,SAAS,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAC;IAC5D,KAAY,GAAG,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAChD,KAAY,aAAa,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC;IAEpD,KAAY,QAAQ,CAAC,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,UAAiB,eAAe,CAAC,CAAC;QAChC,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC;QAChC,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC;QAC7B,CAAC,GAAG,EAAE,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;KACnC;IAED,UAAiB,cAAc,CAAC,CAAC,GAAG,GAAG;QACrC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,SAAS,MAAM,EAAE,CAAC;QACxB,IAAI,EAAE,aAAa,CAAC;QACpB,IAAI,EAAE,CAAC,CAAC;KACT;CACF;AAED;;;;GAIG;AACH,QAAA,MAAM,gBAAgB,wBAAyD,CAAC;AAEhF,OAAO,EAAE,gBAAgB,EAAE,CAAC"}
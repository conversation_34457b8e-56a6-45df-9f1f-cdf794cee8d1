language: node_js
node_js:
- '10'
script:
- set -e
- npm run test
- npm run coveralls
# after_success:
# - git config --global user.email "<EMAIL>"
# - git config --global user.name "Travis CI"
# - npm config set git-tag-version=false
# - NPM_VERSION=$(npm version patch)
# - git commit -a -m "${NPM_VERSION:1}" -m "[ci skip]"
# - git remote remove origin
# - git remote add origin https://${GITHUB_TOKEN}@github.com/xpl/get-source.git
# - git push origin HEAD:master
# deploy:
#   provider: npm
#   email: <EMAIL>
#   api_key:
#     secure: jEWoCdiL3qadEKV36Aw1On1JNNvzSKIaZELVp0NiQLoWwbnTXzkXJuabHGDUQFnD9VXjhqtduS0GMpaV//HOXalSR72s8+VnnLYxsVj+Aslh1kDEwXW3OsQ2O5PSZY5nmGVYuZVjwFeOa70+cvMd0nE1v9HNgJBhhzeKiewgzusGrGBaW3ovXz9Bf7ITsbkVO55SbW8CroKILrQiPBTSqEUqH0Vx+ucHtb+gfJfOs7kXoCkjf8tu/yZjs5NIaHt1lKoWOmUlG3z7CjtFenieuzlQRe48jSQKnbXh5yJmziKvbiiwEfFnPhzPZqPKiXHDkBOr7Fm+geMSJcbRlu4lhB/aUfDoT5vlabnQsTcxz1wTKW+N/WR2xdl4kc5HPF9Tnx4c/MDVvQnC05NCRtcrtZNAla9r9pG/zmIvmFiP0ulPgDok8+Mq4GrDoNd5T4Dt8Xk+uD+rENjifYNetIU3Zcq7uslkwaoDZq29V/tSdbHVtXjMw+FSbUk5jJxD/4j3FxDaQGjOkjN/kqxcWc1IH5xi9bG0wCD5sKsdadPAuEMCJRFIlRP+EtyLD3CKwFYPKCQuTTvPJnZ6IOtCNGWI4Qe0eYSrmwURIdUkyxUkdeGZhjrTsGtJN7WXKq+JD2JU88978o3ZQ+CN1iqBaL8yhlDt8vhPtkzVqZXWI1LAB9A=
env:
  global:
    secure: SXc3ilPr7p+uac/b3ibx27zFEuKotjA0FLHkkFaU5Y74Ek++Yh++cmBbXjz8yS2XtaEmvr+EL/Z7cUsD7hbs787+lyc16AJNEf1l3PKwsFx7djsOBdXFf0mVPhmlx2BbAtgrVylPlVM0yzBDUTWOm2lFtMFp8v2vjJeSnePaWV2Gf7T4hpm4S4U0fXeVRFynFIDo+TfyJoNG6zg6iuUcIjiY5zrvE8U6pg3TtRUaQx8+JWZYdukiKEjPZ2mUHHGaHDRouM99wFz7Y1WTBkxYvyIQFNuf9zzJ/IRMFiLMhxPvSbIYHpNtimCWjYL/Hw105BWOxoTOFa2lsRT5dJAxv3LfroWrEG6vnV5BeJ35Ogcy4Mqf0N3lrMjo/vUgbn6nP2MTyADqBjNdJ0T6tqRSY4sE2M0nXddC/9/ONHOnqzOtAxqS72MJ93ZzSJ0VvMIaf2rygAnIHVHe7mzV3EEhs6l5APQYybYWI8bLD8A75LZeBG1tSBvdPdf5ny6l1GSyoQ1qIntfrl/FcGiJKbGVRl6by/XdRIGA9HOenOHdBzLrB58VF0NkOKzmizlu3O51LW21Yt/HwPRAoGb9t/7KAOT6otKplIk8qy/tTzIc0fZm6mG8FmRQsisIrfWTiWh37jSZU9FGdNGkF/tjjZ/bmEPiJ56fWiXvzVv9CQAeF/0=
